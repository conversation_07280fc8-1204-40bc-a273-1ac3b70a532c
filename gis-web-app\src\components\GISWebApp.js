 import React, { useState, useRef, useEffect } from 'react';
import { MapPin, Layers, Search, Info, Plus, Minus, RotateCcw } from 'lucide-react';

const GISWebApp = () => {
  const mapRef = useRef(null);
  const [mapCenter, setMapCenter] = useState({ lat: 33.6844, lng: 73.0479 }); // Islamabad coordinates
  const [zoom, setZoom] = useState(10);
  const [markers, setMarkers] = useState([]);
  const [selectedLayer, setSelectedLayer] = useState('street');
  const [searchQuery, setSearchQuery] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Sample marker data
  const sampleMarkers = [
    { id: 1, lat: 33.6844, lng: 73.0479, name: "Islamabad", type: "city" },
    { id: 2, lat: 33.7077, lng: 73.0659, name: "F-6 Sector", type: "residential" },
    { id: 3, lat: 33.6513, lng: 73.1363, name: "Blue Area", type: "commercial" },
  ];

  const layers = [
    { id: 'street', name: 'Street Map', color: '#4CAF50' },
    { id: 'satellite', name: 'Satellite View', color: '#2196F3' },
    { id: 'terrain', name: 'Terrain', color: '#FF9800' },
    { id: 'hybrid', name: 'Hybrid', color: '#9C27B0' }
  ];

  useEffect(() => {
    setMarkers(sampleMarkers);
  }, []);

  const handleZoomIn = () => {
    if (zoom < 18) setZoom(zoom + 1);
  };

  const handleZoomOut = () => {
    if (zoom > 1) setZoom(zoom - 1);
  };

  const handleResetView = () => {
    setMapCenter({ lat: 33.6844, lng: 73.0479 });
    setZoom(10);
  };

  const handleMapClick = (e) => {
    if (!isDragging) {
      const rect = mapRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Convert pixel coordinates to lat/lng (simplified)
      const newLat = mapCenter.lat + (y - rect.height/2) * -0.001 * (20 - zoom);
      const newLng = mapCenter.lng + (x - rect.width/2) * 0.001 * (20 - zoom);
      
      const newMarker = {
        id: Date.now(),
        lat: newLat,
        lng: newLng,
        name: `Point ${markers.length + 1}`,
        type: 'user'
      };
      
      setMarkers([...markers, newMarker]);
    }
  };

  const handleMouseDown = (e) => {
    setIsDragging(false);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e) => {
    if (e.buttons === 1) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
        setIsDragging(true);
        const sensitivity = 0.0001 * (20 - zoom);
        setMapCenter({
          lat: mapCenter.lat + deltaY * sensitivity,
          lng: mapCenter.lng - deltaX * sensitivity
        });
        setDragStart({ x: e.clientX, y: e.clientY });
      }
    }
  };

  const getMarkerColor = (type) => {
    switch (type) {
      case 'city': return '#FF4444';
      case 'residential': return '#4CAF50';
      case 'commercial': return '#2196F3';
      case 'user': return '#FF9800';
      default: return '#666';
    }
  };

  const getLayerStyle = () => {
    const baseStyle = {
      width: '100%',
      height: '100%',
      position: 'absolute',
      cursor: isDragging ? 'grabbing' : 'grab'
    };

    switch (selectedLayer) {
      case 'satellite':
        return { ...baseStyle, backgroundColor: '#2E5339', backgroundImage: 'radial-gradient(circle at 25% 25%, #4A6741 0%, #2E5339 50%)' };
      case 'terrain':
        return { ...baseStyle, backgroundColor: '#8D6E63', backgroundImage: 'linear-gradient(45deg, #A1887F 25%, #8D6E63 25%, #8D6E63 50%, #A1887F 50%, #A1887F 75%, #8D6E63 75%)' };
      case 'hybrid':
        return { ...baseStyle, backgroundColor: '#37474F', backgroundImage: 'repeating-linear-gradient(45deg, #455A64, #455A64 10px, #37474F 10px, #37474F 20px)' };
      default:
        return { ...baseStyle, backgroundColor: '#E8F5E8', backgroundImage: 'linear-gradient(90deg, #DDD 1px, transparent 1px), linear-gradient(0deg, #DDD 1px, transparent 1px)', backgroundSize: '50px 50px' };
    }
  };

  const renderMarkers = () => {
    return markers.map(marker => {
      // Simple projection calculation
      const x = 50 + (marker.lng - mapCenter.lng) * 1000 * zoom;
      const y = 50 + (mapCenter.lat - marker.lat) * 1000 * zoom;
      
      return (
        <div
          key={marker.id}
          className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
          style={{ left: x, top: y }}
          title={marker.name}
        >
          <div
            className="w-4 h-4 rounded-full border-2 border-white shadow-lg group-hover:scale-125 transition-transform"
            style={{ backgroundColor: getMarkerColor(marker.type) }}
          />
          <div className="absolute top-5 left-1/2 transform -translate-x-1/2 bg-black text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            {marker.name}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="w-full h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className="w-80 bg-white shadow-lg flex flex-col">
        {/* Header */}
        <div className="p-4 bg-blue-600 text-white">
          <h1 className="text-xl font-bold flex items-center gap-2">
            <MapPin className="w-6 h-6" />
            GIS Web Application
          </h1>
          <p className="text-blue-100 text-sm mt-1">Interactive Mapping System</p>
        </div>

        {/* Search */}
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search locations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>



        {/* Map Info */}
        <div className="p-4 border-b">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Info className="w-4 h-4" />
            Map Information
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Zoom Level:</span>
              <span className="font-mono">{zoom}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Center Lat:</span>
              <span className="font-mono">{mapCenter.lat.toFixed(4)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Center Lng:</span>
              <span className="font-mono">{mapCenter.lng.toFixed(4)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Markers:</span>
              <span className="font-mono">{markers.length}</span>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="p-4 flex-1">
          <h3 className="font-semibold mb-3">Marker Legend</h3>
          <div className="space-y-2 text-sm">
            {[
              { type: 'city', label: 'Cities', color: '#FF4444' },
              { type: 'residential', label: 'Residential', color: '#4CAF50' },
              { type: 'commercial', label: 'Commercial', color: '#2196F3' },
              { type: 'user', label: 'User Points', color: '#FF9800' }
            ].map(item => (
              <div key={item.type} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full border border-gray-300"
                  style={{ backgroundColor: item.color }}
                />
                <span>{item.label}</span>
              </div>
            ))}
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-xs text-blue-700">
              💡 Click on the map to add new markers
            </p>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <div
          ref={mapRef}
          className="w-full h-full relative overflow-hidden"
          style={getLayerStyle()}
          onClick={handleMapClick}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={() => setIsDragging(false)}
          onMouseLeave={() => setIsDragging(false)}
        >
          {/* Grid overlay for street map */}
          {selectedLayer === 'street' && (
            <div className="absolute inset-0 opacity-30">
              <div
                className="w-full h-full"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: `${50 * zoom / 10}px ${50 * zoom / 10}px`
                }}
              />
            </div>
          )}

          {/* Markers */}
          {renderMarkers()}

          {/* Coordinate display */}
          <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 text-white px-3 py-2 rounded text-sm font-mono">
            {mapCenter.lat.toFixed(6)}, {mapCenter.lng.toFixed(6)}
          </div>
        </div>

        {/* Map Controls */}
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <button
            onClick={handleZoomIn}
            className="w-10 h-10 bg-white rounded-lg shadow-lg hover:bg-gray-50 flex items-center justify-center transition-colors"
            title="Zoom In"
          >
            <Plus className="w-5 h-5" />
          </button>
          <button
            onClick={handleZoomOut}
            className="w-10 h-10 bg-white rounded-lg shadow-lg hover:bg-gray-50 flex items-center justify-center transition-colors"
            title="Zoom Out"
          >
            <Minus className="w-5 h-5" />
          </button>
          <button
            onClick={handleResetView}
            className="w-10 h-10 bg-white rounded-lg shadow-lg hover:bg-gray-50 flex items-center justify-center transition-colors"
            title="Reset View"
          >
            <RotateCcw className="w-5 h-5" />
          </button>
          
          {/* Layer Controls */}
          <div className="bg-white rounded-lg shadow-lg p-2 mt-2">
            <div className="text-xs text-gray-600 mb-2 flex items-center gap-1">
              <Layers className="w-3 h-3" />
              Layers
            </div>
            <div className="space-y-1">
              {layers.map(layer => (
                <button
                  key={layer.id}
                  onClick={() => setSelectedLayer(layer.id)}
                  className={`w-full p-2 rounded text-xs text-left transition-colors ${
                    selectedLayer === layer.id
                      ? 'bg-blue-500 text-white'
                      : 'hover:bg-gray-100 text-gray-700'
                  }`}
                  title={layer.name}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-full border border-current"
                      style={{ backgroundColor: selectedLayer === layer.id ? 'white' : layer.color }}
                    />
                    <span className="truncate">{layer.name}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Scale bar */}
        <div className="absolute bottom-4 right-4 bg-white px-3 py-2 rounded shadow-lg">
          <div className="text-xs text-gray-600 mb-1">Scale</div>
          <div className="flex items-center gap-2">
            <div className="w-16 h-1 bg-black" />
            <span className="text-xs font-mono">{Math.round(1000 / zoom)} m</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GISWebApp;